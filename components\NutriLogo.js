import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Path, G } from 'react-native-svg';

const NutriLogo = ({ 
  size = 'medium', 
  color = '#6B7C5A', 
  style = {},
  showText = true 
}) => {
  const sizes = {
    small: { fontSize: 20, leafSize: 16, spacing: 8 },
    medium: { fontSize: 32, leafSize: 24, spacing: 12 },
    large: { fontSize: 48, leafSize: 36, spacing: 16 },
    xlarge: { fontSize: 64, leafSize: 48, spacing: 20 }
  };

  const currentSize = sizes[size] || sizes.medium;

  const LeafIcon = ({ size: leafSize, color }) => (
    <Svg 
      width={leafSize} 
      height={leafSize} 
      viewBox="0 0 80 80"
      style={{ marginLeft: currentSize.spacing }}
    >
      <G>
        {/* Main leaf shape */}
        <Path
          d="M10 50 Q35 15, 70 25 Q60 45, 50 60 Q30 70, 10 50 Z"
          fill={color}
        />
        {/* Leaf vein */}
        <Path
          d="M10 50 Q25 40, 40 35 Q50 32, 60 30"
          stroke="#ffffff"
          strokeWidth="2"
          fill="none"
        />
      </G>
    </Svg>
  );

  return (
    <View style={[styles.container, style]}>
      {showText && (
        <Text style={[styles.text, { fontSize: currentSize.fontSize, color }]}>
          NutriAI
        </Text>
      )}
      <LeafIcon size={currentSize.leafSize} color={color} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontFamily: 'Inter-Bold',
    fontWeight: '700',
    letterSpacing: -0.5,
  },
});

export default NutriLogo;
