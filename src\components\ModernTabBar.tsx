import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  useDerivedValue,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

interface TabItemProps {
  route: any;
  index: number;
  isFocused: boolean;
  onPress: () => void;
  onLongPress: () => void;
  totalTabs: number;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

const TabItem: React.FC<TabItemProps> = ({
  route,
  index,
  isFocused,
  onPress,
  onLongPress,
  totalTabs,
}) => {
  const scale = useSharedValue(1);
  const translateY = useSharedValue(0);
  const iconScale = useSharedValue(1);
  const labelOpacity = useSharedValue(isFocused ? 1 : 0.6);
  const backgroundOpacity = useSharedValue(isFocused ? 1 : 0);

  React.useEffect(() => {
    scale.value = withSpring(isFocused ? 1.1 : 1, { damping: 15, stiffness: 300 });
    translateY.value = withSpring(isFocused ? -2 : 0, { damping: 15, stiffness: 300 });
    iconScale.value = withSpring(isFocused ? 1.2 : 1, { damping: 15, stiffness: 300 });
    labelOpacity.value = withTiming(isFocused ? 1 : 0.6, { duration: 200 });
    backgroundOpacity.value = withTiming(isFocused ? 1 : 0, { duration: 200 });
  }, [isFocused]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value },
    ],
  }));

  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: iconScale.value }],
  }));

  const labelAnimatedStyle = useAnimatedStyle(() => ({
    opacity: labelOpacity.value,
  }));

  const backgroundAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backgroundOpacity.value,
    transform: [{ scale: interpolate(backgroundOpacity.value, [0, 1], [0.8, 1]) }],
  }));

  const handlePressIn = () => {
    'worklet';
    scale.value = withSpring(0.95, { damping: 15, stiffness: 400 });
  };

  const handlePressOut = () => {
    'worklet';
    scale.value = withSpring(isFocused ? 1.1 : 1, { damping: 15, stiffness: 400 });
  };

  const getIconName = (): keyof typeof Ionicons.glyphMap => {
    switch (route.name) {
      case 'Home':
        return isFocused ? 'home' : 'home-outline';
      case 'Recipes':
        return isFocused ? 'restaurant' : 'restaurant-outline';
      case 'Scanner':
        return isFocused ? 'scan' : 'scan-outline';
      case 'Plan':
        return isFocused ? 'calendar' : 'calendar-outline';
      case 'Profile':
        return isFocused ? 'person' : 'person-outline';
      default:
        return 'help-outline';
    }
  };

  const getLabel = () => {
    switch (route.name) {
      case 'Home': return 'Home';
      case 'Recipes': return 'Recipes';
      case 'Scanner': return 'Scanner';
      case 'Plan': return 'Plan';
      case 'Profile': return 'Profile';
      default: return route.name;
    }
  };

  return (
    <AnimatedTouchableOpacity
      style={[styles.tabItem, animatedStyle]}
      onPress={onPress}
      onLongPress={onLongPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
    >
      {/* Active Background */}
      <Animated.View style={[styles.activeBackground, backgroundAnimatedStyle]}>
        <LinearGradient
          colors={[Colors.brandMuted, Colors.brandSubtle]}
          style={styles.activeGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      {/* Icon */}
      <Animated.View style={[styles.iconContainer, iconAnimatedStyle]}>
        <Ionicons
          name={getIconName()}
          size={24}
          color={isFocused ? Colors.white : Colors.mutedForeground}
        />
      </Animated.View>

      {/* Label */}
      <Animated.Text style={[
        styles.label,
        labelAnimatedStyle,
        { color: isFocused ? Colors.white : Colors.mutedForeground }
      ]}>
        {getLabel()}
      </Animated.Text>

      {/* Active Indicator Dot */}
      {isFocused && (
        <Animated.View style={[styles.activeDot, backgroundAnimatedStyle]} />
      )}
    </AnimatedTouchableOpacity>
  );
};

export const ModernTabBar: React.FC<TabBarProps> = ({ state, descriptors, navigation }) => {
  const insets = useSafeAreaInsets();
  const tabBarOpacity = useSharedValue(1);
  const tabBarTranslateY = useSharedValue(0);

  const animatedTabBarStyle = useAnimatedStyle(() => ({
    opacity: tabBarOpacity.value,
    transform: [{ translateY: tabBarTranslateY.value }],
  }));

  return (
    <Animated.View style={[styles.container, { paddingBottom: insets.bottom }, animatedTabBarStyle]}>
      {/* Background Blur Effect */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={[Colors.glass, Colors.glassStrong]}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        <View style={styles.borderTop} />
      </View>

      {/* Tab Items */}
      <View style={styles.tabContainer}>
        {state.routes.map((route: any, index: number) => {
          const { options } = descriptors[route.key];
          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          return (
            <TabItem
              key={route.key}
              route={route}
              index={index}
              isFocused={isFocused}
              onPress={onPress}
              onLongPress={onLongPress}
              totalTabs={state.routes.length}
            />
          );
        })}
      </View>

      {/* Floating Action Button for Scanner */}
      <View style={styles.fabContainer}>
        <TouchableOpacity
          style={styles.fab}
          onPress={() => navigation.navigate('Scanner')}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={[Colors.brand, Colors.brandSecondary]}
            style={styles.fabGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="scan" size={28} color={Colors.brandForeground} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.lg,
  },

  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: BorderRadius.xxxxl,
    borderTopRightRadius: BorderRadius.xxxxl,
    overflow: 'hidden',
    ...Shadows.xl,
  },

  backgroundGradient: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
  },

  borderTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
  },

  tabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: Spacing.lg,
    minHeight: 60,
  },

  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    position: 'relative',
  },

  activeBackground: {
    position: 'absolute',
    top: 2,
    left: 2,
    right: 2,
    bottom: 2,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    ...Shadows.md,
  },

  activeGradient: {
    flex: 1,
    backgroundColor: Colors.brand,
  },

  iconContainer: {
    marginBottom: Spacing.xs,
    zIndex: 2,
  },

  label: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.semibold,
    color: Colors.mutedForeground,
    textAlign: 'center',
    zIndex: 2,
  },

  activeDot: {
    position: 'absolute',
    bottom: -2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.brand,
  },

  // Floating Action Button
  fabContainer: {
    position: 'absolute',
    top: -25,
    left: '50%',
    marginLeft: -25,
    zIndex: 10,
  },

  fab: {
    width: 50,
    height: 50,
    borderRadius: 25,
    ...Shadows.lg,
    elevation: 8,
  },

  fabGradient: {
    flex: 1,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
