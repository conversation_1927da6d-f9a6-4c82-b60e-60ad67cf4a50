import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface ModernTimePickerProps {
  value: string;
  onTimeChange: (time: string) => void;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconColor: string;
}

const { width } = Dimensions.get('window');

export const ModernTimePicker: React.FC<ModernTimePickerProps> = ({
  value,
  onTimeChange,
  label,
  icon,
  iconColor,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Predefined time options for easier, more reliable selection
  const timeOptions = [
    '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30',
    '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
    '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30',
    '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30'
  ];

  const formatDisplayTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const handleTimeSelect = (selectedTime: string) => {
    onTimeChange(selectedTime);
    setIsVisible(false);
  };

  const handleCancel = () => {
    setIsVisible(false);
  };

  const handleOpen = () => {
    // Small delay to prevent conflicts with other modals
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  };



  return (
    <>
      <TouchableOpacity style={styles.timeInputContainer} onPress={handleOpen}>
        <View style={styles.timeInputHeader}>
          <Ionicons name={icon} size={20} color={iconColor} />
          <Text style={styles.timeInputLabel}>{label}</Text>
        </View>
        <View style={styles.timeDisplayContainer}>
          <Text style={styles.timeDisplayText}>{formatDisplayTime(value)}</Text>
          <Ionicons name="chevron-down" size={16} color="#6B7C5A" />
        </View>
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent
        animationType="none"
        onRequestClose={handleCancel}
      >
        <Animated.View
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(200)}
          style={styles.modalOverlay}
        >
          <TouchableOpacity
            style={styles.modalBackdrop}
            activeOpacity={1}
            onPress={handleCancel}
          />
          
          <Animated.View
            entering={FadeIn.delay(100).duration(300)}
            exiting={FadeOut.duration(200)}
            style={styles.modalContent}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select {label} Time</Text>
              <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
                <Ionicons name="close" size={24} color="#6B7C5A" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.timeOptionsContainer} showsVerticalScrollIndicator={false}>
              <View style={styles.timeGrid}>
                {timeOptions.map((time) => (
                  <TouchableOpacity
                    key={time}
                    style={[
                      styles.timeOption,
                      value === time && styles.selectedTimeOption
                    ]}
                    onPress={() => handleTimeSelect(time)}
                  >
                    <Text style={[
                      styles.timeOptionText,
                      value === time && styles.selectedTimeOptionText
                    ]}>
                      {formatDisplayTime(time)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </Animated.View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  timeInputContainer: {
    marginBottom: 20,
  },
  timeInputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeInputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: 8,
  },
  timeDisplayContainer: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
  },
  timeDisplayText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 24,
    width: width * 0.85,
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(107, 124, 90, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
  },
  closeButton: {
    padding: 4,
  },
  pickerContainer: {
    padding: 24,
  },
  pickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pickerColumn: {
    height: 200,
    width: 80,
  },
  pickerScrollContent: {
    paddingVertical: 75,
  },
  pickerItem: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginVertical: 2,
  },
  selectedPickerItem: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
  },
  pickerItemText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#6B7280',
  },
  selectedPickerItemText: {
    color: '#6B7C5A',
    fontWeight: '700',
  },
  pickerSeparator: {
    fontSize: 24,
    fontWeight: '700',
    color: '#6B7C5A',
    marginHorizontal: 16,
  },
  modalActions: {
    flexDirection: 'row',
    padding: 24,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 16,
    paddingVertical: 14,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#6B7C5A',
    borderRadius: 16,
    paddingVertical: 14,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  timeOptionsContainer: {
    maxHeight: 300,
    marginVertical: 16,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  timeOption: {
    width: '30%',
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginBottom: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  selectedTimeOption: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  timeOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7C5A',
  },
  selectedTimeOptionText: {
    color: 'white',
    fontWeight: '600',
  },
});
