import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Path, G } from 'react-native-svg';
import { Typography } from '../constants/Typography';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
} from 'react-native-reanimated';

interface NutriAILogoProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'light' | 'dark';
  showIcon?: boolean;
  animated?: boolean;
}

const NutriAILogo: React.FC<NutriAILogoProps> = ({
  size = 'medium',
  variant = 'dark',
  showIcon = true,
  animated = false,
}) => {
  const pulseValue = useSharedValue(1);
  const rotateValue = useSharedValue(0);

  // Remove pulse animations for better containment

  // Remove animated styles for better containment

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          container: styles.containerSmall,
          text: styles.textSmall,
          subtext: styles.subtextSmall,
        };
      case 'large':
        return {
          container: styles.containerLarge,
          text: styles.textLarge,
          subtext: styles.subtextLarge,
        };
      default:
        return {
          container: styles.containerMedium,
          text: styles.textMedium,
          subtext: styles.subtextMedium,
        };
    }
  };

  const getColorStyles = () => {
    return variant === 'light'
      ? {
          text: styles.textLight,
          subtext: styles.subtextLight,
          icon: '#FFFFFF',
        }
      : {
          text: styles.textDark,
          subtext: styles.subtextDark,
          icon: '#6B7C5A',
        };
  };

  const sizeStyles = getSizeStyles();
  const colorStyles = getColorStyles();

  // Custom leaf icon component
  const LeafIcon = ({ size: leafSize, color }: { size: number; color: string }) => (
    <Svg
      width={leafSize}
      height={leafSize}
      viewBox="0 0 80 80"
      style={{ marginLeft: 8 }}
    >
      <G>
        {/* Main leaf shape */}
        <Path
          d="M10 50 Q35 15, 70 25 Q60 45, 50 60 Q30 70, 10 50 Z"
          fill={color}
        />
        {/* Leaf vein */}
        <Path
          d="M10 50 Q25 40, 40 35 Q50 32, 60 30"
          stroke="#ffffff"
          strokeWidth="2"
          fill="none"
        />
      </G>
    </Svg>
  );

  return (
    <View style={[styles.container, sizeStyles.container]}>
      <View style={styles.textContainer}>
        <View style={styles.logoRow}>
          <Text style={[
            size === 'small' ? Typography.brandLogoSmall : Typography.brandLogo,
            sizeStyles.text,
            colorStyles.text,
            { flexWrap: 'nowrap' }
          ]}>
            NutriAI
          </Text>
          {showIcon && (
            <LeafIcon
              size={size === 'small' ? 16 : size === 'large' ? 32 : 24}
              color={colorStyles.icon}
            />
          )}
        </View>
        {size !== 'small' && (
          <Text style={[
            Typography.brandTagline,
            sizeStyles.subtext,
            colorStyles.subtext
          ]}>
            Smart Nutrition Tracking
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: '100%',
    overflow: 'hidden',
  },
  containerSmall: {
    gap: 6,
    paddingHorizontal: 8,
  },
  containerMedium: {
    gap: 10,
    paddingHorizontal: 12,
  },
  containerLarge: {
    gap: 12,
    paddingHorizontal: 16,
    maxWidth: '90%',
  },

  textContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandText: {
    fontWeight: '900',
    letterSpacing: -1,
    fontFamily: 'System',
  },
  aiText: {
    fontWeight: '300',
    fontStyle: 'italic',
  },
  textSmall: {
    fontSize: 18,
  },
  textMedium: {
    fontSize: 28,
  },
  textLarge: {
    fontSize: 42,
  },
  tagline: {
    fontWeight: '500',
    letterSpacing: 0.5,
    marginTop: 4,
  },
  subtextSmall: {
    fontSize: 10,
  },
  subtextMedium: {
    fontSize: 14,
  },
  subtextLarge: {
    fontSize: 18,
  },
  textDark: {
    color: '#6B7C5A',
  },
  subtextDark: {
    color: '#8B9A7A',
  },
  textLight: {
    color: '#FFFFFF',
  },
  subtextLight: {
    color: 'rgba(255, 255, 255, 0.9)',
  },
});

export default NutriAILogo;
