import React from 'react';
import { View, StyleSheet, ImageBackground } from 'react-native';

interface NutritionBackgroundProps {
  children: React.ReactNode;
  variant?: 'onboarding' | 'main';
}

const NutritionBackground: React.FC<NutritionBackgroundProps> = ({
  children,
  variant = 'main',
}) => {
  // Use the same food/nutrition image as homepage
  const backgroundImage = 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80';

  return (
    <View style={styles.container}>
      {/* Beautiful Background with Food Image - Same as Homepage */}
      <ImageBackground
        source={{ uri: backgroundImage }}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        {/* White Overlay for Content Readability */}
        <View style={[
          styles.overlay,
          variant === 'onboarding' && styles.onboardingOverlay
        ]} />
      </ImageBackground>

      {/* Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.85)',
  },
  onboardingOverlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.92)',
  },
  content: {
    flex: 1,
    zIndex: 10,
  },
});

export default NutritionBackground;
