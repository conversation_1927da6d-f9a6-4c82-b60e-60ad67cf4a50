import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  SlideInLeft,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface Option {
  id: string;
  label: string;
  description?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  color?: string;
}

interface MultiSelectCardProps {
  title: string;
  options: Option[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  maxSelections?: number;
  minSelections?: number;
  allowMultiple?: boolean;
}

const MultiSelectCard: React.FC<MultiSelectCardProps> = ({
  title,
  options,
  selectedValues,
  onSelectionChange,
  maxSelections,
  minSelections = 0,
  allowMultiple = true,
}) => {
  const handleOptionPress = (optionId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (!allowMultiple) {
      // Single selection mode
      onSelectionChange([optionId]);
      return;
    }

    // Multiple selection mode
    const isSelected = selectedValues.includes(optionId);
    
    if (isSelected) {
      // Remove selection if minimum not reached
      if (selectedValues.length > minSelections) {
        onSelectionChange(selectedValues.filter(id => id !== optionId));
      }
    } else {
      // Add selection if maximum not reached
      if (!maxSelections || selectedValues.length < maxSelections) {
        onSelectionChange([...selectedValues, optionId]);
      }
    }
  };

  return (
    <Animated.View entering={FadeInUp.duration(600)} style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <ScrollView 
        style={styles.optionsContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.optionsContent}
      >
        {options.map((option, index) => (
          <OptionCard
            key={option.id}
            option={option}
            isSelected={selectedValues.includes(option.id)}
            onPress={() => handleOptionPress(option.id)}
            index={index}
          />
        ))}
      </ScrollView>
    </Animated.View>
  );
};

interface OptionCardProps {
  option: Option;
  isSelected: boolean;
  onPress: () => void;
  index: number;
}

const OptionCard: React.FC<OptionCardProps> = ({ option, isSelected, onPress, index }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSpring(0.95);
    setTimeout(() => {
      scale.value = withSpring(1);
      onPress();
    }, 100);
  };

  return (
    <Animated.View
      entering={SlideInLeft.delay(200 + index * 100).duration(600)}
      style={[styles.optionCard, animatedStyle]}
    >
      <TouchableOpacity onPress={handlePress}>
        <View
          style={[
            styles.optionCardContent,
            isSelected && styles.optionCardSelected
          ]}
        >
          <View style={styles.optionContent}>
            {option.icon && (
              <View style={[
                styles.optionIcon,
                { backgroundColor: option.color || '#6B7C5A' }
              ]}>
                <Ionicons
                  name={option.icon}
                  size={24}
                  color="white"
                />
              </View>
            )}

            <View style={styles.optionText}>
              <Text style={[
                styles.optionLabel,
                isSelected && styles.optionLabelSelected
              ]}>
                {option.label}
              </Text>
              {option.description && (
                <Text style={[
                  styles.optionDescription,
                  isSelected && styles.optionDescriptionSelected
                ]}>
                  {option.description}
                </Text>
              )}
            </View>

            <View style={[
              styles.checkboxContainer,
              isSelected && styles.checkboxSelected
            ]}>
              {isSelected && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 24,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  optionsContainer: {
    flex: 1,
  },
  optionsContent: {
    paddingBottom: 16,
  },
  optionCard: {
    marginBottom: 16,
    borderRadius: 24,
    overflow: 'hidden',
  },
  optionCardContent: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  optionCardSelected: {
    backgroundColor: '#8B9A7A',
    borderColor: '#6B7C5A',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  optionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  optionText: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  optionLabelSelected: {
    color: '#FFFFFF',
  },
  optionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  optionDescriptionSelected: {
    color: '#F3F4F6',
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    backgroundColor: '#FFFFFF',
  },
  checkboxSelected: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
});

export default MultiSelectCard;
