import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { useOnboarding } from '../../contexts/OnboardingContext';
import NutritionBackground from '../NutritionBackground';

interface OnboardingLayoutProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  onNext?: () => void;
  onBack?: () => void;
  nextButtonText?: string;
  showSkip?: boolean;
  onSkip?: () => void;
  nextDisabled?: boolean;
  showProgress?: boolean;
}

const OnboardingLayout: React.FC<OnboardingLayoutProps> = ({
  title,
  subtitle,
  children,
  onNext,
  onBack,
  nextButtonText = 'Continue',
  showSkip = false,
  onSkip,
  nextDisabled = false,
  showProgress = true,
}) => {
  const { getProgressPercentage, canGoPrevious, saveProgress } = useOnboarding();

  const handleNext = async () => {
    if (onNext) {
      await saveProgress();
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onNext();
    }
  };

  const handleBack = () => {
    if (onBack) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onBack();
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onSkip();
    }
  };

  return (
    <NutritionBackground variant="onboarding">
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Header */}
      <Animated.View entering={FadeInDown.duration(600)} style={styles.header}>
        <View style={styles.headerRow}>
          {canGoPrevious() ? (
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons name="arrow-back" size={24} color="#6B7C5A" />
            </TouchableOpacity>
          ) : (
            <View style={styles.backButton} />
          )}

          {showSkip && (
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipText}>Skip</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Progress Bar */}
        {showProgress && (
          <Animated.View entering={SlideInLeft.delay(300).duration(600)} style={styles.progressContainer}>
            <View style={styles.progressTrack}>
              <Animated.View
                style={[
                  styles.progressFill,
                  { width: `${getProgressPercentage()}%` }
                ]}
                entering={SlideInLeft.delay(500).duration(800)}
              />
            </View>
            <Text style={styles.progressText}>{getProgressPercentage()}% Complete</Text>
          </Animated.View>
        )}
      </Animated.View>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={true}
      >
        {/* Title Section */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.titleSection}>
          <Text style={styles.title}>{title}</Text>
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </Animated.View>

        {/* Main Content */}
        <Animated.View
          entering={FadeInUp.delay(400).duration(600)}
          style={styles.mainContent}
        >
          {children}
        </Animated.View>

        {/* Navigation Buttons */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.navigationContainer}>
          {onNext && (
            <TouchableOpacity
              style={[styles.nextButton, nextDisabled && styles.nextButtonDisabled]}
              onPress={handleNext}
              disabled={nextDisabled}
            >
              <View style={[styles.nextButtonContent, nextDisabled && styles.nextButtonContentDisabled]}>
                <Text style={[styles.nextButtonText, nextDisabled && styles.nextButtonTextDisabled]}>
                  {nextButtonText}
                </Text>
                <Ionicons
                  name="arrow-forward"
                  size={20}
                  color={nextDisabled ? "#9CA3AF" : "white"}
                />
              </View>
            </TouchableOpacity>
          )}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
    </NutritionBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 32, // Increased from 24 to match content padding
    paddingTop: 60,
    paddingBottom: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 2,
    borderColor: '#8B9A7A',
  },
  skipText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressTrack: {
    width: '100%',
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 32, // Increased from 24 to prevent form cropping
    paddingBottom: 40,
  },
  titleSection: {
    marginBottom: 32,
    alignItems: 'center',
    paddingTop: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 26,
  },
  mainContent: {
    flex: 1,
    minHeight: 400,
  },
  navigationContainer: {
    paddingTop: 32,
    paddingBottom: 20,
  },
  nextButton: {
    borderRadius: 32,
    overflow: 'hidden',
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    gap: 12,
    backgroundColor: '#6B7C5A',
  },
  nextButtonContentDisabled: {
    backgroundColor: '#E5E7EB',
  },
  nextButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white', // Keep white for button text on green background
    letterSpacing: 0.5,
  },
  nextButtonTextDisabled: {
    color: '#9CA3AF',
  },
});

export default OnboardingLayout;
