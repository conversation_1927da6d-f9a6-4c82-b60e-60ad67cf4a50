import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Animated, {
  FadeInUp,
  SlideInLeft,
} from 'react-native-reanimated';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import { ModernInput, ModernSelect } from '../../components/onboarding/ModernInput';
import MultiSelectCard from '../../components/onboarding/MultiSelectCard';

type HealthGoalsScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'HealthGoals'>;

const HealthGoalsScreen: React.FC = () => {
  const navigation = useNavigation<HealthGoalsScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [localData, setLocalData] = useState({
    weightGoal: data.weightGoal,
    targetWeight: data.targetWeight.toString(),
    fitnessObjectives: data.fitnessObjectives,
    healthConditions: data.healthConditions,
  });

  const weightGoalOptions = [
    { label: 'Lose Weight', value: 'lose' },
    { label: 'Maintain Weight', value: 'maintain' },
    { label: 'Gain Weight', value: 'gain' },
  ];

  const fitnessObjectiveOptions = [
    {
      id: 'muscle_gain',
      label: 'Build Muscle',
      description: 'Increase muscle mass and strength',
      icon: 'fitness' as const,
      color: '#6B7C5A',
    },
    {
      id: 'fat_loss',
      label: 'Lose Fat',
      description: 'Reduce body fat percentage',
      icon: 'trending-down' as const,
      color: '#8B9A7A',
    },
    {
      id: 'endurance',
      label: 'Improve Endurance',
      description: 'Enhance cardiovascular fitness',
      icon: 'heart' as const,
      color: '#5A6B4A',
    },
    {
      id: 'flexibility',
      label: 'Increase Flexibility',
      description: 'Improve mobility and range of motion',
      icon: 'body' as const,
      color: '#7A8B6A',
    },
    {
      id: 'general_health',
      label: 'General Health',
      description: 'Overall wellness and vitality',
      icon: 'leaf' as const,
      color: '#6B7C5A',
    },
  ];

  const healthConditionOptions = [
    {
      id: 'diabetes',
      label: 'Diabetes',
      description: 'Type 1 or Type 2 diabetes',
      icon: 'medical' as const,
      color: '#DC2626',
    },
    {
      id: 'hypertension',
      label: 'High Blood Pressure',
      description: 'Hypertension management',
      icon: 'heart-circle' as const,
      color: '#EA580C',
    },
    {
      id: 'cholesterol',
      label: 'High Cholesterol',
      description: 'Cholesterol level management',
      icon: 'pulse' as const,
      color: '#D97706',
    },
    {
      id: 'thyroid',
      label: 'Thyroid Issues',
      description: 'Hyper or hypothyroidism',
      icon: 'body' as const,
      color: '#7C3AED',
    },
    {
      id: 'none',
      label: 'No Health Conditions',
      description: 'I have no known health conditions',
      icon: 'checkmark-circle' as const,
      color: '#059669',
    },
  ];

  const updateLocalData = (field: string, value: any) => {
    setLocalData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    // Update onboarding context
    updateData('weightGoal', localData.weightGoal);
    updateData('targetWeight', parseFloat(localData.targetWeight) || data.weight);
    updateData('fitnessObjectives', localData.fitnessObjectives);
    updateData('healthConditions', localData.healthConditions);
    
    nextStep();
    navigation.navigate('DietaryPreferences');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const isFormValid = () => {
    return localData.weightGoal && localData.fitnessObjectives.length > 0;
  };

  return (
    <OnboardingLayout
      title="What are your health goals?"
      subtitle="Help us create a personalized plan for you"
      onNext={handleNext}
      onBack={handleBack}
      nextDisabled={!isFormValid()}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.formContainer}>
          <Animated.View entering={SlideInLeft.delay(200).duration(600)}>
            <ModernSelect
              label="Weight Goal"
              value={localData.weightGoal}
              onSelect={(value) => updateLocalData('weightGoal', value)}
              options={weightGoalOptions}
              placeholder="Select your weight goal"
              icon="trending-up"
            />
          </Animated.View>

          {localData.weightGoal && localData.weightGoal !== 'maintain' && (
            <Animated.View entering={FadeInUp.delay(300).duration(600)}>
              <ModernInput
                label="Target Weight"
                value={localData.targetWeight}
                onChangeText={(text) => {
                  const cleanText = text.replace(/[^0-9.]/g, '');
                  const parts = cleanText.split('.');
                  if (parts.length <= 2) {
                    updateLocalData('targetWeight', cleanText);
                  }
                }}
                placeholder="Enter your target weight"
                keyboardType="numeric"
                icon="flag"
                suffix="kg"
                maxLength={6}
              />
            </Animated.View>
          )}

          <Animated.View entering={SlideInLeft.delay(400).duration(600)} style={styles.sectionContainer}>
            <MultiSelectCard
              title="Fitness Objectives (Select all that apply)"
              options={fitnessObjectiveOptions}
              selectedValues={localData.fitnessObjectives}
              onSelectionChange={(values) => updateLocalData('fitnessObjectives', values)}
              allowMultiple={true}
              minSelections={1}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(600).duration(600)} style={styles.sectionContainer}>
            <MultiSelectCard
              title="Health Conditions (Optional)"
              options={healthConditionOptions}
              selectedValues={localData.healthConditions}
              onSelectionChange={(values) => updateLocalData('healthConditions', values)}
              allowMultiple={true}
            />
          </Animated.View>
        </View>
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  formContainer: {
    paddingTop: 16,
  },
  sectionContainer: {
    marginTop: 32,
    minHeight: 200,
  },
});

export default HealthGoalsScreen;
