import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Animated, { SlideInLeft } from 'react-native-reanimated';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import { ModernInput } from '../../components/onboarding/ModernInput';

type NutritionGoalsScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'NutritionGoals'>;

const NutritionGoalsScreen: React.FC = () => {
  const navigation = useNavigation<NutritionGoalsScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [localData, setLocalData] = useState({
    caloriesGoal: data.caloriesGoal.toString(),
    proteinGoal: data.proteinGoal.toString(),
    carbsPercentage: data.carbsPercentage.toString(),
    fatPercentage: data.fatPercentage.toString(),
  });

  // Calculate recommended calories based on user data
  useEffect(() => {
    if (data.weight && data.height && data.age && data.gender && data.activityLevel) {
      const bmr = calculateBMR();
      const tdee = calculateTDEE(bmr);
      const adjustedCalories = adjustForGoal(tdee);
      
      setLocalData(prev => ({
        ...prev,
        caloriesGoal: Math.round(adjustedCalories).toString(),
        proteinGoal: Math.round(adjustedCalories * 0.3 / 4).toString(), // 30% of calories from protein
      }));
    }
  }, [data]);

  const calculateBMR = () => {
    // Mifflin-St Jeor Equation
    if (data.gender === 'male') {
      return 10 * data.weight + 6.25 * data.height - 5 * data.age + 5;
    } else {
      return 10 * data.weight + 6.25 * data.height - 5 * data.age - 161;
    }
  };

  const calculateTDEE = (bmr: number) => {
    const activityMultipliers = {
      sedentary: 1.2,
      lightly_active: 1.375,
      moderately_active: 1.55,
      very_active: 1.725,
    };
    return bmr * (activityMultipliers[data.activityLevel as keyof typeof activityMultipliers] || 1.55);
  };

  const adjustForGoal = (tdee: number) => {
    switch (data.weightGoal) {
      case 'lose':
        return tdee - 500; // 500 calorie deficit
      case 'gain':
        return tdee + 300; // 300 calorie surplus
      default:
        return tdee; // maintain
    }
  };

  const updateLocalData = (field: string, value: string) => {
    setLocalData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    updateData('caloriesGoal', parseInt(localData.caloriesGoal) || 2000);
    updateData('proteinGoal', parseInt(localData.proteinGoal) || 150);
    updateData('carbsPercentage', parseInt(localData.carbsPercentage) || 45);
    updateData('fatPercentage', parseInt(localData.fatPercentage) || 30);
    
    nextStep();
    navigation.navigate('NotificationSettings');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const isFormValid = () => {
    const calories = parseInt(localData.caloriesGoal);
    const protein = parseInt(localData.proteinGoal);
    const carbs = parseInt(localData.carbsPercentage);
    const fat = parseInt(localData.fatPercentage);
    
    return calories >= 1200 && calories <= 4000 &&
           protein >= 50 && protein <= 300 &&
           carbs >= 20 && carbs <= 70 &&
           fat >= 15 && fat <= 50 &&
           (carbs + fat) <= 85; // Leave room for protein
  };

  return (
    <OnboardingLayout
      title="Nutrition Goals"
      subtitle="Set your daily nutrition targets"
      onNext={handleNext}
      onBack={handleBack}
      nextDisabled={!isFormValid()}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.formContainer}>
          <Animated.View entering={SlideInLeft.delay(200).duration(600)}>
            <ModernInput
              label="Daily Calorie Goal"
              value={localData.caloriesGoal}
              onChangeText={(text) => updateLocalData('caloriesGoal', text.replace(/[^0-9]/g, ''))}
              placeholder="2000"
              keyboardType="numeric"
              icon="flame"
              suffix="cal"
              maxLength={4}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(300).duration(600)}>
            <ModernInput
              label="Daily Protein Goal"
              value={localData.proteinGoal}
              onChangeText={(text) => updateLocalData('proteinGoal', text.replace(/[^0-9]/g, ''))}
              placeholder="150"
              keyboardType="numeric"
              icon="fitness"
              suffix="g"
              maxLength={3}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(400).duration(600)}>
            <Text style={styles.sectionTitle}>Macronutrient Distribution</Text>
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(500).duration(600)}>
            <ModernInput
              label="Carbohydrates"
              value={localData.carbsPercentage}
              onChangeText={(text) => updateLocalData('carbsPercentage', text.replace(/[^0-9]/g, ''))}
              placeholder="45"
              keyboardType="numeric"
              icon="nutrition"
              suffix="%"
              maxLength={2}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(600).duration(600)}>
            <ModernInput
              label="Fats"
              value={localData.fatPercentage}
              onChangeText={(text) => updateLocalData('fatPercentage', text.replace(/[^0-9]/g, ''))}
              placeholder="30"
              keyboardType="numeric"
              icon="water"
              suffix="%"
              maxLength={2}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(700).duration(600)}>
            <View style={styles.infoCard}>
              <Text style={styles.infoText}>
                Protein will make up the remaining percentage. 
                Recommended: 45% carbs, 30% fat, 25% protein.
              </Text>
            </View>
          </Animated.View>
        </View>
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  formContainer: {
    paddingTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 16,
    marginTop: 8,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  infoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  infoText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default NutritionGoalsScreen;
