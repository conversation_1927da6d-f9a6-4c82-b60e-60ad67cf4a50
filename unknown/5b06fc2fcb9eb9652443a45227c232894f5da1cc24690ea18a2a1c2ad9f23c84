import AsyncStorage from '@react-native-async-storage/async-storage';

// Database keys for organized storage
const DB_KEYS = {
  USER_PROFILE: 'user_profile',
  MEAL_LOGS: 'meal_logs',
  WEEKLY_PLANS: 'weekly_plans',
  HEALTH_DATA: 'health_data',
  SCAN_HISTORY: 'scan_history',
  RECIPES: 'recipes',
  PREFERENCES: 'preferences',
  ONBOARDING_DATA: 'onboarding_data',
  NUTRITION_TRACKING: 'nutrition_tracking',
  ACHIEVEMENTS: 'achievements',
  NOTIFICATIONS: 'notifications'
};

// Database interfaces
export interface UserProfileDB {
  id: string;
  data: any;
  createdAt: string;
  updatedAt: string;
}

export interface MealLogDB {
  id: string;
  name: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  timestamp: number;
  date: string;
  scanData?: any;
  nutritionData?: any;
}

export interface WeeklyPlanDB {
  id: string;
  weekStartDate: string;
  plan: any;
  customizations: any;
  createdAt: string;
  isActive: boolean;
}

export interface HealthDataDB {
  id: string;
  date: string;
  steps: number;
  heartRate?: number;
  weight?: number;
  waterIntake: number;
  sleepHours?: number;
  energyLevel?: number;
  mood?: string;
  timestamp: number;
}

export interface ScanHistoryDB {
  id: string;
  imageUri: string;
  analysis: any;
  timestamp: number;
  date: string;
  mealLogged: boolean;
}

export interface RecipeDB {
  id: string;
  title: string;
  ingredients: string[];
  instructions: string[];
  nutrition: any;
  tags: string[];
  imageUrl?: string;
  cookTime?: string;
  difficulty?: string;
  servings?: number;
  createdAt: string;
  isFavorite: boolean;
}

export interface PreferencesDB {
  id: string;
  notificationSettings: any;
  dietaryPreferences: string[];
  mealTimes: any;
  units: 'metric' | 'imperial';
  theme: 'light' | 'dark' | 'auto';
  updatedAt: string;
}

class DatabaseService {
  private static instance: DatabaseService;

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Generic database operations
  private async setData<T>(key: string, data: T): Promise<void> {
    try {
      const jsonData = JSON.stringify(data);
      await AsyncStorage.setItem(key, jsonData);
    } catch (error) {
      console.error(`Error saving data to ${key}:`, error);
      throw error;
    }
  }

  private async getData<T>(key: string): Promise<T | null> {
    try {
      const jsonData = await AsyncStorage.getItem(key);
      return jsonData ? JSON.parse(jsonData) : null;
    } catch (error) {
      console.error(`Error getting data from ${key}:`, error);
      return null;
    }
  }

  private async appendToArray<T>(key: string, newItem: T): Promise<void> {
    try {
      const existingData = await this.getData<T[]>(key) || [];
      existingData.push(newItem);
      await this.setData(key, existingData);
    } catch (error) {
      console.error(`Error appending to ${key}:`, error);
      throw error;
    }
  }

  private async updateArrayItem<T extends { id: string }>(
    key: string, 
    itemId: string, 
    updateData: Partial<T>
  ): Promise<void> {
    try {
      const existingData = await this.getData<T[]>(key) || [];
      const itemIndex = existingData.findIndex(item => item.id === itemId);
      
      if (itemIndex !== -1) {
        existingData[itemIndex] = { ...existingData[itemIndex], ...updateData };
        await this.setData(key, existingData);
      }
    } catch (error) {
      console.error(`Error updating item in ${key}:`, error);
      throw error;
    }
  }

  private async removeArrayItem(key: string, itemId: string): Promise<void> {
    try {
      const existingData = await this.getData<any[]>(key) || [];
      const filteredData = existingData.filter(item => item.id !== itemId);
      await this.setData(key, filteredData);
    } catch (error) {
      console.error(`Error removing item from ${key}:`, error);
      throw error;
    }
  }

  // User Profile Operations
  async saveUserProfile(profile: any): Promise<void> {
    const profileData: UserProfileDB = {
      id: 'main_profile',
      data: profile,
      createdAt: profile.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    await this.setData(DB_KEYS.USER_PROFILE, profileData);
  }

  async getUserProfile(): Promise<any | null> {
    const profileData = await this.getData<UserProfileDB>(DB_KEYS.USER_PROFILE);
    return profileData?.data || null;
  }

  // Meal Logs Operations
  async saveMealLog(meal: Omit<MealLogDB, 'id' | 'date'>): Promise<string> {
    const mealLog: MealLogDB = {
      ...meal,
      id: `meal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      date: new Date().toISOString().split('T')[0]
    };
    
    await this.appendToArray(DB_KEYS.MEAL_LOGS, mealLog);
    return mealLog.id;
  }

  async getMealLogs(date?: string): Promise<MealLogDB[]> {
    const allMeals = await this.getData<MealLogDB[]>(DB_KEYS.MEAL_LOGS) || [];
    
    if (date) {
      return allMeals.filter(meal => meal.date === date);
    }
    
    return allMeals.sort((a, b) => b.timestamp - a.timestamp);
  }

  async getRecentMeals(limit: number = 10): Promise<MealLogDB[]> {
    const allMeals = await this.getMealLogs();
    return allMeals.slice(0, limit);
  }

  // Weekly Plans Operations
  async saveWeeklyPlan(plan: any, customizations: any = {}): Promise<string> {
    const weekPlan: WeeklyPlanDB = {
      id: `plan_${Date.now()}`,
      weekStartDate: this.getWeekStartDate(),
      plan,
      customizations,
      createdAt: new Date().toISOString(),
      isActive: true
    };

    // Deactivate previous plans
    const existingPlans = await this.getData<WeeklyPlanDB[]>(DB_KEYS.WEEKLY_PLANS) || [];
    const updatedPlans = existingPlans.map(p => ({ ...p, isActive: false }));
    updatedPlans.push(weekPlan);
    
    await this.setData(DB_KEYS.WEEKLY_PLANS, updatedPlans);
    return weekPlan.id;
  }

  async getActiveWeeklyPlan(): Promise<WeeklyPlanDB | null> {
    const plans = await this.getData<WeeklyPlanDB[]>(DB_KEYS.WEEKLY_PLANS) || [];
    return plans.find(plan => plan.isActive) || null;
  }

  async getAllWeeklyPlans(): Promise<WeeklyPlanDB[]> {
    return await this.getData<WeeklyPlanDB[]>(DB_KEYS.WEEKLY_PLANS) || [];
  }

  async deleteWeeklyPlan(): Promise<void> {
    console.log('🗑️ Deleting all weekly plans from database...');
    await this.setData(DB_KEYS.WEEKLY_PLANS, []);
    console.log('✅ All weekly plans deleted from database');
  }

  // Health Data Operations
  async saveHealthData(healthData: Omit<HealthDataDB, 'id' | 'date' | 'timestamp'>): Promise<string> {
    const health: HealthDataDB = {
      ...healthData,
      id: `health_${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      timestamp: Date.now()
    };
    
    await this.appendToArray(DB_KEYS.HEALTH_DATA, health);
    return health.id;
  }

  async getHealthData(date?: string): Promise<HealthDataDB[]> {
    const allHealth = await this.getData<HealthDataDB[]>(DB_KEYS.HEALTH_DATA) || [];
    
    if (date) {
      return allHealth.filter(health => health.date === date);
    }
    
    return allHealth.sort((a, b) => b.timestamp - a.timestamp);
  }

  async getTodaysHealthData(): Promise<HealthDataDB | null> {
    const today = new Date().toISOString().split('T')[0];
    const todaysData = await this.getHealthData(today);
    return todaysData[0] || null;
  }

  // Scan History Operations
  async saveScanHistory(scan: Omit<ScanHistoryDB, 'id' | 'date'>): Promise<string> {
    const scanRecord: ScanHistoryDB = {
      ...scan,
      id: `scan_${Date.now()}`,
      date: new Date().toISOString().split('T')[0]
    };
    
    await this.appendToArray(DB_KEYS.SCAN_HISTORY, scanRecord);
    return scanRecord.id;
  }

  async getScanHistory(limit?: number): Promise<ScanHistoryDB[]> {
    const allScans = await this.getData<ScanHistoryDB[]>(DB_KEYS.SCAN_HISTORY) || [];
    const sortedScans = allScans.sort((a, b) => b.timestamp - a.timestamp);
    
    return limit ? sortedScans.slice(0, limit) : sortedScans;
  }

  // Recipe Operations
  async saveRecipe(recipe: Omit<RecipeDB, 'id' | 'createdAt'>): Promise<string> {
    const recipeRecord: RecipeDB = {
      ...recipe,
      id: `recipe_${Date.now()}`,
      createdAt: new Date().toISOString()
    };
    
    await this.appendToArray(DB_KEYS.RECIPES, recipeRecord);
    return recipeRecord.id;
  }

  async getRecipes(): Promise<RecipeDB[]> {
    return await this.getData<RecipeDB[]>(DB_KEYS.RECIPES) || [];
  }

  async getFavoriteRecipes(): Promise<RecipeDB[]> {
    const allRecipes = await this.getRecipes();
    return allRecipes.filter(recipe => recipe.isFavorite);
  }

  async toggleRecipeFavorite(recipeId: string): Promise<void> {
    const recipes = await this.getRecipes();
    const recipeIndex = recipes.findIndex(r => r.id === recipeId);
    
    if (recipeIndex !== -1) {
      recipes[recipeIndex].isFavorite = !recipes[recipeIndex].isFavorite;
      await this.setData(DB_KEYS.RECIPES, recipes);
    }
  }

  // Preferences Operations
  async savePreferences(preferences: Omit<PreferencesDB, 'id' | 'updatedAt'>): Promise<void> {
    const prefs: PreferencesDB = {
      ...preferences,
      id: 'main_preferences',
      updatedAt: new Date().toISOString()
    };
    
    await this.setData(DB_KEYS.PREFERENCES, prefs);
  }

  async getPreferences(): Promise<PreferencesDB | null> {
    return await this.getData<PreferencesDB>(DB_KEYS.PREFERENCES);
  }

  // Utility Methods
  private getWeekStartDate(): string {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - dayOfWeek);
    return startOfWeek.toISOString().split('T')[0];
  }

  // Database Maintenance
  async clearOldData(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const cutoffTimestamp = cutoffDate.getTime();

    try {
      // Clean old meal logs
      const mealLogs = await this.getMealLogs();
      const recentMeals = mealLogs.filter(meal => meal.timestamp > cutoffTimestamp);
      await this.setData(DB_KEYS.MEAL_LOGS, recentMeals);

      // Clean old health data
      const healthData = await this.getHealthData();
      const recentHealth = healthData.filter(health => health.timestamp > cutoffTimestamp);
      await this.setData(DB_KEYS.HEALTH_DATA, recentHealth);

      // Clean old scan history
      const scanHistory = await this.getScanHistory();
      const recentScans = scanHistory.filter(scan => scan.timestamp > cutoffTimestamp);
      await this.setData(DB_KEYS.SCAN_HISTORY, recentScans);

      console.log(`Database cleanup completed. Kept data from last ${daysToKeep} days.`);
    } catch (error) {
      console.error('Error during database cleanup:', error);
    }
  }

  async getDatabaseSize(): Promise<{ [key: string]: number }> {
    const sizes: { [key: string]: number } = {};
    
    for (const [name, key] of Object.entries(DB_KEYS)) {
      try {
        const data = await AsyncStorage.getItem(key);
        sizes[name] = data ? data.length : 0;
      } catch (error) {
        sizes[name] = 0;
      }
    }
    
    return sizes;
  }

  async exportAllData(): Promise<any> {
    const exportData: any = {};
    
    for (const [name, key] of Object.entries(DB_KEYS)) {
      try {
        exportData[name] = await this.getData(key);
      } catch (error) {
        console.error(`Error exporting ${name}:`, error);
        exportData[name] = null;
      }
    }
    
    return {
      exportDate: new Date().toISOString(),
      version: '1.0',
      data: exportData
    };
  }

  async clearAllData(): Promise<void> {
    try {
      for (const key of Object.values(DB_KEYS)) {
        await AsyncStorage.removeItem(key);
      }
      console.log('All database data cleared successfully');
    } catch (error) {
      console.error('Error clearing database:', error);
      throw error;
    }
  }
}

export default DatabaseService.getInstance();
