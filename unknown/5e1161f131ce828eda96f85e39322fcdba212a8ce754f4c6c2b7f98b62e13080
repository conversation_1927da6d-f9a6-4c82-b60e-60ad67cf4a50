import AsyncStorage from '@react-native-async-storage/async-storage';

export interface WeightGoal {
  startWeight: number; // kg
  targetWeight: number; // kg
  goalWeightLoss: number; // kg to lose
  targetDate: string; // ISO date string
  startDate: string; // ISO date string
  weeklyGoal: number; // kg per week
  dailyCalorieDeficit: number; // calories per day needed
  createdAt: number; // timestamp
}

export interface DailyProgress {
  date: string; // YYYY-MM-DD format
  caloriesConsumed: number;
  caloriesFromScanner: number;
  caloriesFromWeeklyPlan: number;
  caloriesFromManualLog: number;
  estimatedWeightChange: number; // kg (negative for loss)
  mealsLogged: number;
  scannedMeals: number;
  weeklyPlanMeals: number;
  createdAt: number;
  updatedAt: number;
}

export interface WeightProgress {
  currentEstimatedWeight: number;
  totalWeightLost: number;
  remainingWeightToLose: number;
  progressPercentage: number;
  daysElapsed: number;
  daysRemaining: number;
  averageDailyProgress: number;
  isOnTrack: boolean;
  projectedCompletionDate: string;
}

class WeightGoalTracker {
  private static instance: WeightGoalTracker;
  private readonly WEIGHT_GOAL_KEY = 'weight_goal_data';
  private readonly DAILY_PROGRESS_KEY = 'daily_progress_data';
  private readonly CALORIES_PER_KG = 7700; // Approximate calories in 1kg of body fat

  static getInstance(): WeightGoalTracker {
    if (!WeightGoalTracker.instance) {
      WeightGoalTracker.instance = new WeightGoalTracker();
    }
    return WeightGoalTracker.instance;
  }

  // Initialize weight goal from onboarding data
  async initializeWeightGoal(onboardingData: {
    currentWeight: number;
    targetWeight: number;
    timeframe: number; // weeks
    activityLevel: string;
  }): Promise<WeightGoal> {
    try {
      const startDate = new Date();
      const targetDate = new Date();
      targetDate.setDate(startDate.getDate() + (onboardingData.timeframe * 7));

      const goalWeightLoss = onboardingData.currentWeight - onboardingData.targetWeight;
      const weeklyGoal = goalWeightLoss / onboardingData.timeframe;
      const dailyCalorieDeficit = (weeklyGoal * this.CALORIES_PER_KG) / 7;

      const weightGoal: WeightGoal = {
        startWeight: onboardingData.currentWeight,
        targetWeight: onboardingData.targetWeight,
        goalWeightLoss,
        targetDate: targetDate.toISOString(),
        startDate: startDate.toISOString(),
        weeklyGoal,
        dailyCalorieDeficit,
        createdAt: Date.now()
      };

      await AsyncStorage.setItem(this.WEIGHT_GOAL_KEY, JSON.stringify(weightGoal));
      console.log('✅ Weight goal initialized:', weightGoal);
      
      return weightGoal;
    } catch (error) {
      console.error('❌ Error initializing weight goal:', error);
      throw error;
    }
  }

  // Get current weight goal
  async getWeightGoal(): Promise<WeightGoal | null> {
    try {
      const goalJson = await AsyncStorage.getItem(this.WEIGHT_GOAL_KEY);
      return goalJson ? JSON.parse(goalJson) : null;
    } catch (error) {
      console.error('❌ Error getting weight goal:', error);
      return null;
    }
  }

  // Log calories from different sources
  async logDailyCalories(
    date: string, // YYYY-MM-DD
    calories: number,
    source: 'scanner' | 'weekly_plan' | 'manual'
  ): Promise<void> {
    try {
      const progressData = await this.getDailyProgress(date) || this.createEmptyDailyProgress(date);
      
      // Update calories based on source
      switch (source) {
        case 'scanner':
          progressData.caloriesFromScanner += calories;
          progressData.scannedMeals += 1;
          break;
        case 'weekly_plan':
          progressData.caloriesFromWeeklyPlan += calories;
          progressData.weeklyPlanMeals += 1;
          break;
        case 'manual':
          progressData.caloriesFromManualLog += calories;
          break;
      }

      // Update totals
      progressData.caloriesConsumed = 
        progressData.caloriesFromScanner + 
        progressData.caloriesFromWeeklyPlan + 
        progressData.caloriesFromManualLog;
      
      progressData.mealsLogged = 
        progressData.scannedMeals + 
        progressData.weeklyPlanMeals;

      // Calculate estimated weight change
      const weightGoal = await this.getWeightGoal();
      if (weightGoal) {
        const calorieDeficit = weightGoal.dailyCalorieDeficit - progressData.caloriesConsumed;
        progressData.estimatedWeightChange = -calorieDeficit / this.CALORIES_PER_KG;
      }

      progressData.updatedAt = Date.now();

      // Store updated progress
      await this.storeDailyProgress(progressData);
      
      console.log(`📊 Logged ${calories} calories from ${source} for ${date}`);
    } catch (error) {
      console.error('❌ Error logging daily calories:', error);
      throw error;
    }
  }

  // Get daily progress for a specific date
  async getDailyProgress(date: string): Promise<DailyProgress | null> {
    try {
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      if (!allProgressJson) return null;

      const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);
      return allProgress[date] || null;
    } catch (error) {
      console.error('❌ Error getting daily progress:', error);
      return null;
    }
  }

  // Create empty daily progress entry
  private createEmptyDailyProgress(date: string): DailyProgress {
    return {
      date,
      caloriesConsumed: 0,
      caloriesFromScanner: 0,
      caloriesFromWeeklyPlan: 0,
      caloriesFromManualLog: 0,
      estimatedWeightChange: 0,
      mealsLogged: 0,
      scannedMeals: 0,
      weeklyPlanMeals: 0,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  // Store daily progress
  private async storeDailyProgress(progress: DailyProgress): Promise<void> {
    try {
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      const allProgress: { [date: string]: DailyProgress } = allProgressJson ? JSON.parse(allProgressJson) : {};
      
      allProgress[progress.date] = progress;
      
      // Keep only last 90 days of data
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90);
      const cutoffDateStr = cutoffDate.toISOString().split('T')[0];
      
      const filteredProgress: { [date: string]: DailyProgress } = {};
      Object.entries(allProgress).forEach(([date, data]) => {
        if (date >= cutoffDateStr) {
          filteredProgress[date] = data;
        }
      });
      
      await AsyncStorage.setItem(this.DAILY_PROGRESS_KEY, JSON.stringify(filteredProgress));
    } catch (error) {
      console.error('❌ Error storing daily progress:', error);
      throw error;
    }
  }

  // Calculate current weight progress
  async calculateWeightProgress(): Promise<WeightProgress | null> {
    try {
      const weightGoal = await this.getWeightGoal();
      if (!weightGoal) return null;

      const startDate = new Date(weightGoal.startDate);
      const targetDate = new Date(weightGoal.targetDate);
      const now = new Date();

      const daysElapsed = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const totalDays = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const daysRemaining = Math.max(0, totalDays - daysElapsed);

      // Calculate total weight lost from daily progress
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      let totalWeightLost = 0;
      
      if (allProgressJson) {
        const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);
        totalWeightLost = Object.values(allProgress)
          .reduce((sum, day) => sum + Math.abs(Math.min(0, day.estimatedWeightChange)), 0);
      }

      const currentEstimatedWeight = weightGoal.startWeight - totalWeightLost;
      const remainingWeightToLose = Math.max(0, currentEstimatedWeight - weightGoal.targetWeight);
      const progressPercentage = Math.min(100, (totalWeightLost / weightGoal.goalWeightLoss) * 100);
      
      const averageDailyProgress = daysElapsed > 0 ? totalWeightLost / daysElapsed : 0;
      const requiredDailyProgress = daysRemaining > 0 ? remainingWeightToLose / daysRemaining : 0;
      const isOnTrack = averageDailyProgress >= requiredDailyProgress * 0.8; // 80% tolerance

      // Project completion date
      let projectedCompletionDate = targetDate.toISOString();
      if (averageDailyProgress > 0 && remainingWeightToLose > 0) {
        const projectedDaysRemaining = remainingWeightToLose / averageDailyProgress;
        const projectedDate = new Date();
        projectedDate.setDate(projectedDate.getDate() + projectedDaysRemaining);
        projectedCompletionDate = projectedDate.toISOString();
      }

      return {
        currentEstimatedWeight,
        totalWeightLost,
        remainingWeightToLose,
        progressPercentage,
        daysElapsed,
        daysRemaining,
        averageDailyProgress,
        isOnTrack,
        projectedCompletionDate
      };
    } catch (error) {
      console.error('❌ Error calculating weight progress:', error);
      return null;
    }
  }

  // Get progress for last N days
  async getRecentProgress(days: number = 7): Promise<DailyProgress[]> {
    try {
      const allProgressJson = await AsyncStorage.getItem(this.DAILY_PROGRESS_KEY);
      if (!allProgressJson) return [];

      const allProgress: { [date: string]: DailyProgress } = JSON.parse(allProgressJson);
      
      const recentDates: string[] = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        recentDates.push(date.toISOString().split('T')[0]);
      }

      return recentDates.map(date => 
        allProgress[date] || this.createEmptyDailyProgress(date)
      );
    } catch (error) {
      console.error('❌ Error getting recent progress:', error);
      return [];
    }
  }

  // Clear all data (for testing or reset)
  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([this.WEIGHT_GOAL_KEY, this.DAILY_PROGRESS_KEY]);
      console.log('✅ All weight tracking data cleared');
    } catch (error) {
      console.error('❌ Error clearing weight tracking data:', error);
      throw error;
    }
  }
}

export default WeightGoalTracker.getInstance();
